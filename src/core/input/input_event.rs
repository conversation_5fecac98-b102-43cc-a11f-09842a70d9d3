/// ### Godot-compatible input event system.
///
/// This module provides the main InputEvent enum that represents all possible
/// input events in the system. It follows <PERSON><PERSON>'s input event hierarchy and
/// provides a unified interface for handling keyboard, mouse, and gamepad input.
///
/// ## Event Types
///
/// - **InputEventKey**: Keyboard key press/release events with modifiers
/// - **InputEventMouseButton**: Mouse button press/release events with position
/// - **InputEventMouseMotion**: Mouse movement events with relative motion
/// - **InputEventJoypadButton**: Gamepad button press/release events
/// - **InputEventJoypadMotion**: Analog stick and trigger motion events
/// - **InputEventAction**: Logical action events mapped from physical inputs
///
/// ## Event Properties
///
/// All events include common properties like timestamp and device information
/// for comprehensive input tracking and debugging capabilities.
///
/// ## Examples
///
/// ```rust
/// use verturion::core::input::{Input<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
/// use verturion::core::math::Vector2;
///
/// // Create different types of input events
/// let key_event = InputEvent::key(KeyCode::Space, true, false);
/// let mouse_event = InputEvent::mouse_button(MouseButton::Left, true, 100.0, 200.0);
/// let gamepad_event = InputEvent::joypad_button(JoypadButton::A, true, 0);
///
/// // Handle events in a unified way
/// match key_event {
///     InputEvent::Key { keycode, pressed, .. } => {
///         println!("Key {:?} {}", keycode, if pressed { "pressed" } else { "released" });
///     }
///     _ => {}
/// }
/// ```
use super::{KeyCode, KeyModifiers, MouseButton, MouseMotion, JoypadButton, JoypadAxis, JoypadMotion};

/// ### Unified input event enumeration.
///
/// Represents all possible input events in the system following Godot's
/// input event structure. Each variant contains the specific data needed
/// for that type of input event along with common properties.
///
/// ## Event Variants
///
/// Each event type includes relevant data and common properties like
/// timestamp and device information for comprehensive input handling.
///
/// ## Examples
///
/// ```rust
/// # use verturion::core::input::{InputEvent, KeyCode, MouseButton};
/// // Pattern matching on input events
/// let event = InputEvent::key(KeyCode::Enter, true, false);
/// match event {
///     InputEvent::Key { keycode, pressed, .. } if pressed => {
///         println!("Enter key was pressed!");
///     }
///     InputEvent::MouseButton { button, pressed, .. } => {
///         println!("Mouse button event: {:?}", button);
///     }
///     _ => {}
/// }
/// ```
#[derive(Clone, Debug, PartialEq)]
pub enum InputEvent {
    /// Keyboard key press/release event
    Key {
        /// The key that was pressed or released
        keycode: KeyCode,
        /// Whether the key is currently pressed
        pressed: bool,
        /// Whether this is a key repeat event
        echo: bool,
        /// Modifier keys state during this event
        modifiers: KeyModifiers,
        /// Unicode character for text input (if applicable)
        unicode: Option<char>,
        /// Event timestamp in milliseconds
        timestamp: u64,
    },

    /// Mouse button press/release event
    MouseButton {
        /// The mouse button that was pressed or released
        button: MouseButton,
        /// Whether the button is currently pressed
        pressed: bool,
        /// Mouse position when the event occurred
        position: (f32, f32),
        /// Global mouse position (screen coordinates)
        global_position: (f32, f32),
        /// Click count for double-click detection
        click_count: u8,
        /// Modifier keys state during this event
        modifiers: KeyModifiers,
        /// Event timestamp in milliseconds
        timestamp: u64,
    },

    /// Mouse motion event
    MouseMotion {
        /// Mouse motion data including position and velocity
        motion: MouseMotion,
        /// Modifier keys state during this event
        modifiers: KeyModifiers,
        /// Event timestamp in milliseconds
        timestamp: u64,
    },

    /// Gamepad button press/release event
    JoypadButton {
        /// The gamepad button that was pressed or released
        button: JoypadButton,
        /// Whether the button is currently pressed
        pressed: bool,
        /// Pressure/strength of the button press (0.0 to 1.0)
        pressure: f32,
        /// Controller device index
        device: i32,
        /// Event timestamp in milliseconds
        timestamp: u64,
    },

    /// Gamepad analog motion event
    JoypadMotion {
        /// Analog motion data including axis and value
        motion: JoypadMotion,
        /// Event timestamp in milliseconds
        timestamp: u64,
    },

    /// Logical action event mapped from physical inputs
    Action {
        /// Name of the action that was triggered
        action: String,
        /// Whether the action is currently active
        pressed: bool,
        /// Strength/intensity of the action (0.0 to 1.0)
        strength: f32,
        /// Event timestamp in milliseconds
        timestamp: u64,
    },
}

impl InputEvent {
    /// ### Creates a keyboard input event.
    ///
    /// Constructs a keyboard event with the specified key, press state, and echo flag.
    /// Uses current system time for timestamp and empty modifiers.
    ///
    /// # Parameters
    /// - `keycode`: The key that generated this event
    /// - `pressed`: Whether the key is pressed (true) or released (false)
    /// - `echo`: Whether this is a key repeat event
    ///
    /// # Returns
    /// New InputEvent::Key with the specified parameters.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputEvent, KeyCode};
    /// let key_press = InputEvent::key(KeyCode::Space, true, false);
    /// let key_release = InputEvent::key(KeyCode::Space, false, false);
    /// let key_repeat = InputEvent::key(KeyCode::A, true, true);
    /// ```
    pub fn key(keycode: KeyCode, pressed: bool, echo: bool) -> Self {
        Self::Key {
            keycode,
            pressed,
            echo,
            modifiers: KeyModifiers::new(),
            unicode: None,
            timestamp: Self::current_timestamp(),
        }
    }

    /// ### Creates a keyboard input event with modifiers.
    ///
    /// Constructs a keyboard event with modifier keys and optional Unicode character.
    /// Useful for handling complex key combinations and text input.
    ///
    /// # Parameters
    /// - `keycode`: The key that generated this event
    /// - `pressed`: Whether the key is pressed or released
    /// - `echo`: Whether this is a key repeat event
    /// - `modifiers`: State of modifier keys during this event
    /// - `unicode`: Optional Unicode character for text input
    ///
    /// # Returns
    /// New InputEvent::Key with full parameters.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputEvent, KeyCode, KeyModifiers};
    /// let ctrl_c = InputEvent::key_with_modifiers(
    ///     KeyCode::C,
    ///     true,
    ///     false,
    ///     KeyModifiers::new().with_ctrl(true),
    ///     Some('c')
    /// );
    /// ```
    pub fn key_with_modifiers(
        keycode: KeyCode,
        pressed: bool,
        echo: bool,
        modifiers: KeyModifiers,
        unicode: Option<char>,
    ) -> Self {
        Self::Key {
            keycode,
            pressed,
            echo,
            modifiers,
            unicode,
            timestamp: Self::current_timestamp(),
        }
    }

    /// ### Creates a mouse button input event.
    ///
    /// Constructs a mouse button event with position information.
    /// Uses the same position for both local and global coordinates.
    ///
    /// # Parameters
    /// - `button`: The mouse button that generated this event
    /// - `pressed`: Whether the button is pressed or released
    /// - `x`: Mouse X position when the event occurred
    /// - `y`: Mouse Y position when the event occurred
    ///
    /// # Returns
    /// New InputEvent::MouseButton with the specified parameters.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputEvent, MouseButton};
    /// let left_click = InputEvent::mouse_button(MouseButton::Left, true, 100.0, 200.0);
    /// let right_release = InputEvent::mouse_button(MouseButton::Right, false, 150.0, 250.0);
    /// ```
    pub fn mouse_button(button: MouseButton, pressed: bool, x: f32, y: f32) -> Self {
        Self::MouseButton {
            button,
            pressed,
            position: (x, y),
            global_position: (x, y),
            click_count: 1,
            modifiers: KeyModifiers::new(),
            timestamp: Self::current_timestamp(),
        }
    }

    /// ### Creates a mouse motion input event.
    ///
    /// Constructs a mouse motion event with the specified motion data.
    /// Uses empty modifiers and current timestamp.
    ///
    /// # Parameters
    /// - `motion`: Mouse motion data including position and velocity
    ///
    /// # Returns
    /// New InputEvent::MouseMotion with the specified motion.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputEvent, MouseMotion};
    /// # use verturion::core::math::Vector2;
    /// let motion = MouseMotion::new(
    ///     Vector2::new(100.0, 200.0),
    ///     Vector2::new(5.0, -3.0),
    ///     Vector2::new(1.2, 0.8)
    /// );
    /// let motion_event = InputEvent::mouse_motion(motion);
    /// ```
    pub fn mouse_motion(motion: MouseMotion) -> Self {
        Self::MouseMotion {
            motion,
            modifiers: KeyModifiers::new(),
            timestamp: Self::current_timestamp(),
        }
    }

    /// ### Creates a gamepad button input event.
    ///
    /// Constructs a gamepad button event with pressure information.
    /// Uses full pressure (1.0) for pressed buttons, zero for released.
    ///
    /// # Parameters
    /// - `button`: The gamepad button that generated this event
    /// - `pressed`: Whether the button is pressed or released
    /// - `device`: Controller device index
    ///
    /// # Returns
    /// New InputEvent::JoypadButton with the specified parameters.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputEvent, JoypadButton};
    /// let a_press = InputEvent::joypad_button(JoypadButton::A, true, 0);
    /// let b_release = InputEvent::joypad_button(JoypadButton::B, false, 1);
    /// ```
    pub fn joypad_button(button: JoypadButton, pressed: bool, device: i32) -> Self {
        Self::JoypadButton {
            button,
            pressed,
            pressure: if pressed { 1.0 } else { 0.0 },
            device,
            timestamp: Self::current_timestamp(),
        }
    }

    /// ### Creates a gamepad motion input event.
    ///
    /// Constructs a gamepad motion event with the specified motion data.
    /// Uses current timestamp for the event.
    ///
    /// # Parameters
    /// - `motion`: Gamepad motion data including axis and value
    ///
    /// # Returns
    /// New InputEvent::JoypadMotion with the specified motion.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputEvent, JoypadMotion, JoypadAxis};
    /// let motion = JoypadMotion::new(JoypadAxis::LeftStickX, 0.75, 0);
    /// let motion_event = InputEvent::joypad_motion(motion);
    /// ```
    pub fn joypad_motion(motion: JoypadMotion) -> Self {
        Self::JoypadMotion {
            motion,
            timestamp: Self::current_timestamp(),
        }
    }

    /// ### Creates an action input event.
    ///
    /// Constructs a logical action event mapped from physical inputs.
    /// Actions provide a layer of abstraction over raw input events.
    ///
    /// # Parameters
    /// - `action`: Name of the action that was triggered
    /// - `pressed`: Whether the action is currently active
    /// - `strength`: Intensity of the action (0.0 to 1.0)
    ///
    /// # Returns
    /// New InputEvent::Action with the specified parameters.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::InputEvent;
    /// let jump_action = InputEvent::action("jump".to_string(), true, 1.0);
    /// let move_action = InputEvent::action("move_right".to_string(), true, 0.75);
    /// ```
    pub fn action(action: String, pressed: bool, strength: f32) -> Self {
        Self::Action {
            action,
            pressed,
            strength,
            timestamp: Self::current_timestamp(),
        }
    }

    /// ### Gets the timestamp of this input event.
    ///
    /// Returns the timestamp when this event was created, in milliseconds.
    /// Useful for input timing, debouncing, and event ordering.
    ///
    /// # Returns
    /// Event timestamp in milliseconds.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputEvent, KeyCode};
    /// let event = InputEvent::key(KeyCode::Space, true, false);
    /// let timestamp = event.timestamp();
    /// println!("Event occurred at: {} ms", timestamp);
    /// ```
    pub fn timestamp(&self) -> u64 {
        match self {
            InputEvent::Key { timestamp, .. } => *timestamp,
            InputEvent::MouseButton { timestamp, .. } => *timestamp,
            InputEvent::MouseMotion { timestamp, .. } => *timestamp,
            InputEvent::JoypadButton { timestamp, .. } => *timestamp,
            InputEvent::JoypadMotion { timestamp, .. } => *timestamp,
            InputEvent::Action { timestamp, .. } => *timestamp,
        }
    }

    /// ### Checks if this is a pressed event.
    ///
    /// Returns true if this event represents a press action (key down, button down, etc.).
    /// For motion events, always returns false since they don't have press/release states.
    ///
    /// # Returns
    /// True if this is a press event, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputEvent, KeyCode, MouseButton};
    /// let key_press = InputEvent::key(KeyCode::Space, true, false);
    /// let key_release = InputEvent::key(KeyCode::Space, false, false);
    ///
    /// assert!(key_press.is_pressed());
    /// assert!(!key_release.is_pressed());
    /// ```
    pub fn is_pressed(&self) -> bool {
        match self {
            InputEvent::Key { pressed, .. } => *pressed,
            InputEvent::MouseButton { pressed, .. } => *pressed,
            InputEvent::JoypadButton { pressed, .. } => *pressed,
            InputEvent::Action { pressed, .. } => *pressed,
            InputEvent::MouseMotion { .. } => false,
            InputEvent::JoypadMotion { .. } => false,
        }
    }

    /// ### Checks if this is a released event.
    ///
    /// Returns true if this event represents a release action (key up, button up, etc.).
    /// For motion events, always returns false since they don't have press/release states.
    ///
    /// # Returns
    /// True if this is a release event, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputEvent, KeyCode};
    /// let key_press = InputEvent::key(KeyCode::Space, true, false);
    /// let key_release = InputEvent::key(KeyCode::Space, false, false);
    ///
    /// assert!(!key_press.is_released());
    /// assert!(key_release.is_released());
    /// ```
    pub fn is_released(&self) -> bool {
        match self {
            InputEvent::Key { pressed, .. } => !*pressed,
            InputEvent::MouseButton { pressed, .. } => !*pressed,
            InputEvent::JoypadButton { pressed, .. } => !*pressed,
            InputEvent::Action { pressed, .. } => !*pressed,
            InputEvent::MouseMotion { .. } => false,
            InputEvent::JoypadMotion { .. } => false,
        }
    }

    /// ### Gets the device index for this event.
    ///
    /// Returns the device index for events that support multiple devices.
    /// Returns 0 for keyboard and mouse events (single device).
    ///
    /// # Returns
    /// Device index for this event.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputEvent, JoypadButton, KeyCode};
    /// let gamepad_event = InputEvent::joypad_button(JoypadButton::A, true, 1);
    /// assert_eq!(gamepad_event.device(), 1);
    ///
    /// let keyboard_event = InputEvent::key(KeyCode::Space, true, false);
    /// assert_eq!(keyboard_event.device(), 0);
    /// ```
    pub fn device(&self) -> i32 {
        match self {
            InputEvent::JoypadButton { device, .. } => *device,
            InputEvent::JoypadMotion { motion, .. } => motion.device(),
            _ => 0, // Keyboard and mouse are considered device 0
        }
    }

    /// Gets current system timestamp in milliseconds
    fn current_timestamp() -> u64 {
        // In a real implementation, this would use system time
        // For now, we'll use a simple counter or fixed value
        0
    }
}

impl std::fmt::Display for InputEvent {
    /// ### Formats the InputEvent for display.
    ///
    /// Provides human-readable representation of input events suitable
    /// for debugging and logging purposes.
    ///
    /// # Parameters
    /// - `f`: Formatter for writing the output
    ///
    /// # Returns
    /// Result of the formatting operation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::input::{InputEvent, KeyCode, MouseButton};
    /// let key_event = InputEvent::key(KeyCode::Space, true, false);
    /// println!("{}", key_event); // Shows key event details
    ///
    /// let mouse_event = InputEvent::mouse_button(MouseButton::Left, true, 100.0, 200.0);
    /// println!("{}", mouse_event); // Shows mouse event details
    /// ```
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            InputEvent::Key { keycode, pressed, echo, .. } => {
                write!(f, "Key[{:?}: {}, echo: {}]", keycode,
                       if *pressed { "pressed" } else { "released" }, echo)
            }
            InputEvent::MouseButton { button, pressed, position, .. } => {
                write!(f, "MouseButton[{}: {} at ({:.1}, {:.1})]", button,
                       if *pressed { "pressed" } else { "released" }, position.0, position.1)
            }
            InputEvent::MouseMotion { motion, .. } => {
                write!(f, "MouseMotion[{}]", motion)
            }
            InputEvent::JoypadButton { button, pressed, device, .. } => {
                write!(f, "JoypadButton[{}: {} on device {}]", button,
                       if *pressed { "pressed" } else { "released" }, device)
            }
            InputEvent::JoypadMotion { motion, .. } => {
                write!(f, "JoypadMotion[{}]", motion)
            }
            InputEvent::Action { action, pressed, strength, .. } => {
                write!(f, "Action[{}: {}, strength: {:.2}]", action,
                       if *pressed { "active" } else { "inactive" }, strength)
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::math::Vector2;

    #[test]
    fn test_input_event_key_creation() {
        let event = InputEvent::key(KeyCode::Space, true, false);

        match event {
            InputEvent::Key { keycode, pressed, echo, .. } => {
                assert_eq!(keycode, KeyCode::Space);
                assert!(pressed);
                assert!(!echo);
            }
            _ => panic!("Expected Key event"),
        }

        assert!(event.is_pressed());
        assert!(!event.is_released());
        assert_eq!(event.device(), 0);
    }

    #[test]
    fn test_input_event_key_with_modifiers() {
        let modifiers = KeyModifiers::new().with_ctrl(true).with_shift(true);
        let event = InputEvent::key_with_modifiers(
            KeyCode::C,
            true,
            false,
            modifiers,
            Some('C')
        );

        match event {
            InputEvent::Key { keycode, modifiers, unicode, .. } => {
                assert_eq!(keycode, KeyCode::C);
                assert!(modifiers.ctrl());
                assert!(modifiers.shift());
                assert_eq!(unicode, Some('C'));
            }
            _ => panic!("Expected Key event"),
        }
    }

    #[test]
    fn test_input_event_mouse_button() {
        let event = InputEvent::mouse_button(MouseButton::Left, true, 100.0, 200.0);

        match event {
            InputEvent::MouseButton { button, pressed, position, .. } => {
                assert_eq!(button, MouseButton::Left);
                assert!(pressed);
                assert_eq!(position, (100.0, 200.0));
            }
            _ => panic!("Expected MouseButton event"),
        }

        assert!(event.is_pressed());
        assert!(!event.is_released());
    }

    #[test]
    fn test_input_event_mouse_motion() {
        let motion = MouseMotion::new(
            Vector2::new(100.0, 200.0),
            Vector2::new(5.0, -3.0),
            Vector2::new(1.2, 0.8)
        );
        let event = InputEvent::mouse_motion(motion);

        match event {
            InputEvent::MouseMotion { motion, .. } => {
                assert_eq!(motion.position(), Vector2::new(100.0, 200.0));
                assert_eq!(motion.relative(), Vector2::new(5.0, -3.0));
                assert_eq!(motion.velocity(), Vector2::new(1.2, 0.8));
            }
            _ => panic!("Expected MouseMotion event"),
        }

        // Motion events don't have press/release states
        assert!(!event.is_pressed());
        assert!(!event.is_released());
    }

    #[test]
    fn test_input_event_joypad_button() {
        let event = InputEvent::joypad_button(JoypadButton::A, true, 1);

        match event {
            InputEvent::JoypadButton { button, pressed, pressure, device, .. } => {
                assert_eq!(button, JoypadButton::A);
                assert!(pressed);
                assert_eq!(pressure, 1.0);
                assert_eq!(device, 1);
            }
            _ => panic!("Expected JoypadButton event"),
        }

        assert!(event.is_pressed());
        assert_eq!(event.device(), 1);
    }

    #[test]
    fn test_input_event_joypad_motion() {
        let motion = JoypadMotion::new(JoypadAxis::LeftStickX, 0.75, 0);
        let event = InputEvent::joypad_motion(motion);

        match event {
            InputEvent::JoypadMotion { motion, .. } => {
                assert_eq!(motion.axis(), JoypadAxis::LeftStickX);
                assert_eq!(motion.value(), 0.75);
                assert_eq!(motion.device(), 0);
            }
            _ => panic!("Expected JoypadMotion event"),
        }

        assert_eq!(event.device(), 0);
    }

    #[test]
    fn test_input_event_action() {
        let event = InputEvent::action("jump".to_string(), true, 1.0);

        match &event {
            InputEvent::Action { action, pressed, strength, .. } => {
                assert_eq!(action, "jump");
                assert!(*pressed);
                assert_eq!(*strength, 1.0);
            }
            _ => panic!("Expected Action event"),
        }

        assert!(event.is_pressed());
    }

    #[test]
    fn test_input_event_press_release_states() {
        let press_event = InputEvent::key(KeyCode::Space, true, false);
        let release_event = InputEvent::key(KeyCode::Space, false, false);

        assert!(press_event.is_pressed());
        assert!(!press_event.is_released());

        assert!(!release_event.is_pressed());
        assert!(release_event.is_released());
    }

    #[test]
    fn test_input_event_timestamp() {
        let event = InputEvent::key(KeyCode::Space, true, false);
        let timestamp = event.timestamp();

        // Timestamp should be consistent
        assert_eq!(event.timestamp(), timestamp);
    }

    #[test]
    fn test_input_event_display() {
        let key_event = InputEvent::key(KeyCode::Space, true, false);
        let display = format!("{}", key_event);
        assert!(display.contains("Key"));
        assert!(display.contains("pressed"));

        let mouse_event = InputEvent::mouse_button(MouseButton::Left, false, 50.0, 75.0);
        let display = format!("{}", mouse_event);
        assert!(display.contains("MouseButton"));
        assert!(display.contains("released"));
        assert!(display.contains("50.0"));
        assert!(display.contains("75.0"));
    }

    #[test]
    fn test_input_event_device_indexing() {
        let keyboard_event = InputEvent::key(KeyCode::A, true, false);
        assert_eq!(keyboard_event.device(), 0);

        let mouse_event = InputEvent::mouse_button(MouseButton::Left, true, 0.0, 0.0);
        assert_eq!(mouse_event.device(), 0);

        let gamepad_event = InputEvent::joypad_button(JoypadButton::A, true, 2);
        assert_eq!(gamepad_event.device(), 2);

        let motion = JoypadMotion::new(JoypadAxis::LeftStickX, 0.5, 3);
        let motion_event = InputEvent::joypad_motion(motion);
        assert_eq!(motion_event.device(), 3);
    }
}

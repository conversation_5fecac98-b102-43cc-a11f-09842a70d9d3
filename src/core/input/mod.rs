/// ### Input handling module for Godot-compatible input system.
///
/// This module provides comprehensive input handling capabilities following Godot's
/// input system patterns. It includes support for keyboard, mouse, gamepad/joystick
/// input with seamless integration into the Variant system.
///
/// ## Features
///
/// - **Complete Input Coverage**: Keyboard, mouse, gamepad, and custom input events
/// - **Event-Driven Architecture**: Efficient event processing and state management
/// - **Action Mapping**: Map physical inputs to logical game actions
/// - **Multi-Device Support**: Handle multiple controllers and input devices
/// - **Variant Integration**: Seamless integration with the Variant type system
/// - **Performance Optimized**: Efficient input polling and event processing
/// - **Godot Compatible**: API and behavior patterns matching Godot Engine
///
/// ## Input Event Types
///
/// - **InputEventKey**: Keyboard key press/release events
/// - **InputEventMouseButton**: Mouse button press/release events
/// - **InputEventMouseMotion**: Mouse movement and position tracking
/// - **InputEventJoypadButton**: Gamepad button press/release events
/// - **InputEventJoypadMotion**: Analog stick and trigger input
/// - **InputEventAction**: Logical action events mapped from physical inputs
///
/// ## Examples
///
/// ```rust
/// use verturion::core::input::{InputEvent, KeyCode, MouseButton, JoypadButton};
/// use verturion::core::variant::Variant;
///
/// // Create keyboard input events
/// let key_event = InputEvent::key(KeyCode::Space, true, false);
/// let mouse_event = InputEvent::mouse_button(MouseButton::Left, true, 100.0, 200.0);
/// let gamepad_event = InputEvent::joypad_button(JoypadButton::A, true, 0);
///
/// // Use in Variant system
/// let input_variant = Variant::from(key_event);
/// assert!(input_variant.is_input_event());
///
/// // Action mapping
/// let input_map = InputMap::new();
/// input_map.add_action("jump", KeyCode::Space);
/// input_map.add_action("jump", JoypadButton::A);
/// ```

mod input_event;
mod keyboard;
mod mouse;
mod joystick;
mod input;

pub use input_event::InputEvent;
pub use keyboard::{KeyCode, KeyModifiers};
pub use mouse::{MouseButton, MouseMotion};
pub use joystick::{JoypadButton, JoypadAxis, JoypadMotion};
pub use input::{Input, InputMap, ActionState};

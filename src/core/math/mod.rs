// Vector modules
pub mod vector2;
pub mod vector2i;
pub mod vector3;
pub mod vector3i;
pub mod vector4;
pub mod vector4i;

// Fast inverse square root module
pub mod fast_inv_sqrt;

// Transformation modules
pub mod transform2d;
pub mod quaternion;
pub mod basis;
pub mod plane;

// Rectangle modules
pub mod rect2;
pub mod rect2i;

// Re-export vector types
pub use vector2::Vector2;
pub use vector2i::Vector2i;
pub use vector3::Vector3;
pub use vector3i::Vector3i;
pub use vector4::Vector4;
pub use vector4i::Vector4i;

// Re-export transformation types
pub use transform2d::Transform2D;
pub use quaternion::Quaternion;
pub use basis::Basis;
pub use plane::Plane;

// Re-export rectangle types
pub use rect2::Rect2;
pub use rect2i::Rect2i;

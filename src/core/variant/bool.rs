/// ### Boolean Value Wrapper
///
/// A Godot-compatible wrapper around Rust's bool primitive type that provides
/// type safety, utility methods, and seamless integration with the Variant system.
/// This type is designed to match <PERSON><PERSON>'s bool behavior while maintaining Rust's
/// performance characteristics.
///
/// ## Features
///
/// - **Type Safety**: Prevents accidental mixing of boolean with other types
/// - **Godot Compatibility**: Matches Godot's bool API and behavior patterns
/// - **Performance**: Zero-cost abstraction with Copy semantics
/// - **Integration**: Works seamlessly with Dictionary, Array, and Variant systems
/// - **Logical Operations**: Comprehensive boolean logic operations
///
/// ## Examples
///
/// ```
/// # use verturion::core::variant::Bool;
/// // Create booleans
/// let true_val = Bool::new(true);
/// let false_val = Bool::new(false);
/// let default_val = Bool::default(); // false
///
/// // Logical operations
/// let and_result = true_val.and(false_val);
/// let or_result = true_val.or(false_val);
/// let not_result = true_val.not();
///
/// // Conversions
/// let as_int = true_val.to_int();
/// let as_float = false_val.to_float();
/// ```

use std::fmt;
use std::hash::{Hash, Hasher};

/// ### Boolean wrapper type.
///
/// Provides a type-safe wrapper around bool with Godot-compatible methods
/// and seamless integration with the Variant system.
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub struct Bool {
    /// The wrapped boolean value
    value: bool,
}

impl Bool {
    /// ### True constant.
    pub const TRUE: Self = Self { value: true };

    /// ### False constant.
    pub const FALSE: Self = Self { value: false };

    /// ### Creates a new Bool with the specified value.
    ///
    /// This is the primary constructor for creating Bool instances.
    ///
    /// # Arguments
    /// * `value` - The boolean value to wrap
    ///
    /// # Returns
    /// A new Bool instance containing the specified value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Bool;
    /// let true_val = Bool::new(true);
    /// let false_val = Bool::new(false);
    /// ```
    #[inline]
    pub const fn new(value: bool) -> Self {
        Self { value }
    }

    /// ### Creates a new Bool from a primitive bool value.
    ///
    /// This method provides an alternative constructor that makes the
    /// conversion from primitive types explicit in the code.
    ///
    /// # Arguments
    /// * `value` - The primitive bool value to wrap
    ///
    /// # Returns
    /// A new Bool instance containing the specified value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Bool;
    /// let from_literal = Bool::from_primitive(true);
    /// let from_variable = Bool::from_primitive(some_bool_value);
    /// ```
    #[inline]
    pub const fn from_primitive(value: bool) -> Self {
        Self::new(value)
    }

    /// ### Gets the wrapped boolean value.
    ///
    /// Returns the underlying bool value contained in this Bool wrapper.
    /// This is the primary method for extracting the primitive value.
    ///
    /// # Returns
    /// The wrapped bool value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Bool;
    /// let bool_val = Bool::new(true);
    /// let primitive: bool = bool_val.get();
    /// assert_eq!(primitive, true);
    /// ```
    #[inline]
    pub const fn get(self) -> bool {
        self.value
    }

    /// ### Sets the wrapped boolean value.
    ///
    /// Updates the value contained in this Bool wrapper.
    /// This method provides mutable access to the wrapped value.
    ///
    /// # Arguments
    /// * `value` - The new value to store
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Bool;
    /// let mut bool_val = Bool::new(false);
    /// bool_val.set(true);
    /// assert_eq!(bool_val.get(), true);
    /// ```
    #[inline]
    pub fn set(&mut self, value: bool) {
        self.value = value;
    }

    /// ### Returns the wrapped value as a primitive bool.
    ///
    /// This method provides an alternative to `get()` with a more explicit name
    /// indicating the conversion to primitive type.
    ///
    /// # Returns
    /// The wrapped bool value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Bool;
    /// let bool_val = Bool::new(false);
    /// let primitive = bool_val.as_primitive();
    /// assert_eq!(primitive, false);
    /// ```
    #[inline]
    pub const fn as_primitive(self) -> bool {
        self.value
    }
}

impl Bool {
    /// ### Performs logical AND operation.
    ///
    /// Returns true if both this Bool and the other Bool are true.
    ///
    /// # Arguments
    /// * `other` - The other Bool to AND with
    ///
    /// # Returns
    /// A Bool containing the result of the AND operation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Bool;
    /// let true_val = Bool::new(true);
    /// let false_val = Bool::new(false);
    ///
    /// assert_eq!(true_val.and(true_val), Bool::new(true));
    /// assert_eq!(true_val.and(false_val), Bool::new(false));
    /// assert_eq!(false_val.and(false_val), Bool::new(false));
    /// ```
    #[inline]
    pub fn and(self, other: Self) -> Self {
        Self::new(self.value && other.value)
    }

    /// ### Performs logical OR operation.
    ///
    /// Returns true if either this Bool or the other Bool is true.
    ///
    /// # Arguments
    /// * `other` - The other Bool to OR with
    ///
    /// # Returns
    /// A Bool containing the result of the OR operation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Bool;
    /// let true_val = Bool::new(true);
    /// let false_val = Bool::new(false);
    ///
    /// assert_eq!(true_val.or(false_val), Bool::new(true));
    /// assert_eq!(false_val.or(false_val), Bool::new(false));
    /// assert_eq!(true_val.or(true_val), Bool::new(true));
    /// ```
    #[inline]
    pub fn or(self, other: Self) -> Self {
        Self::new(self.value || other.value)
    }

    /// ### Performs logical NOT operation.
    ///
    /// Returns the logical negation of this Bool.
    ///
    /// # Returns
    /// A Bool containing the negated value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Bool;
    /// let true_val = Bool::new(true);
    /// let false_val = Bool::new(false);
    ///
    /// assert_eq!(true_val.not(), Bool::new(false));
    /// assert_eq!(false_val.not(), Bool::new(true));
    /// ```
    #[inline]
    pub fn not(self) -> Self {
        Self::new(!self.value)
    }

    /// ### Performs logical XOR operation.
    ///
    /// Returns true if exactly one of the Bool values is true.
    ///
    /// # Arguments
    /// * `other` - The other Bool to XOR with
    ///
    /// # Returns
    /// A Bool containing the result of the XOR operation.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Bool;
    /// let true_val = Bool::new(true);
    /// let false_val = Bool::new(false);
    ///
    /// assert_eq!(true_val.xor(false_val), Bool::new(true));
    /// assert_eq!(true_val.xor(true_val), Bool::new(false));
    /// assert_eq!(false_val.xor(false_val), Bool::new(false));
    /// ```
    #[inline]
    pub fn xor(self, other: Self) -> Self {
        Self::new(self.value ^ other.value)
    }
}

impl Bool {
    /// ### Converts the boolean to an integer value.
    ///
    /// Creates a new Int wrapper containing 1 for true or 0 for false.
    /// This follows the standard boolean-to-integer conversion.
    ///
    /// # Returns
    /// An Int containing 1 for true, 0 for false.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Bool, Int};
    /// let true_val = Bool::new(true);
    /// let false_val = Bool::new(false);
    ///
    /// assert_eq!(true_val.to_int().get(), 1);
    /// assert_eq!(false_val.to_int().get(), 0);
    /// ```
    #[inline]
    pub fn to_int(self) -> super::Int {
        super::Int::new(if self.value { 1 } else { 0 })
    }

    /// ### Converts the boolean to a floating-point value.
    ///
    /// Creates a new Float wrapper containing 1.0 for true or 0.0 for false.
    /// This follows the standard boolean-to-float conversion.
    ///
    /// # Returns
    /// A Float containing 1.0 for true, 0.0 for false.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Bool, Float};
    /// let true_val = Bool::new(true);
    /// let false_val = Bool::new(false);
    ///
    /// assert_eq!(true_val.to_float().get(), 1.0);
    /// assert_eq!(false_val.to_float().get(), 0.0);
    /// ```
    #[inline]
    pub fn to_float(self) -> super::Float {
        super::Float::new(if self.value { 1.0 } else { 0.0 })
    }

    /// ### Converts the boolean to a Short (16-bit integer).
    ///
    /// Creates a new Short wrapper containing 1 for true or 0 for false.
    ///
    /// # Returns
    /// A Short containing 1 for true, 0 for false.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Bool, Short};
    /// let true_val = Bool::new(true);
    /// assert_eq!(true_val.to_short().get(), 1);
    /// ```
    #[inline]
    pub fn to_short(self) -> super::Short {
        super::Short::new(if self.value { 1 } else { 0 })
    }

    /// ### Converts the boolean to an Int8 (8-bit integer).
    ///
    /// Creates a new Int8 wrapper containing 1 for true or 0 for false.
    ///
    /// # Returns
    /// An Int8 containing 1 for true, 0 for false.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Bool, Int8};
    /// let false_val = Bool::new(false);
    /// assert_eq!(false_val.to_int8().get(), 0);
    /// ```
    #[inline]
    pub fn to_int8(self) -> super::Int8 {
        super::Int8::new(if self.value { 1 } else { 0 })
    }
}

impl Default for Bool {
    /// ### Creates a default Bool with value false.
    ///
    /// This implementation allows Bool to be used in contexts where
    /// a default value is needed, such as in collections or when
    /// using the `Default::default()` method.
    ///
    /// # Returns
    /// A Bool with value false.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Bool;
    /// let default_bool = Bool::default();
    /// assert_eq!(default_bool.get(), false);
    ///
    /// let also_default: Bool = Default::default();
    /// assert_eq!(also_default, Bool::new(false));
    /// ```
    #[inline]
    fn default() -> Self {
        Self::new(false)
    }
}

impl fmt::Display for Bool {
    /// ### Formats the Bool for display.
    ///
    /// Provides a human-readable string representation of the boolean value.
    /// The output matches the standard formatting of bool values.
    ///
    /// # Arguments
    /// * `f` - The formatter to write to
    ///
    /// # Returns
    /// A formatting result.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Bool;
    /// let true_val = Bool::new(true);
    /// assert_eq!(format!("{}", true_val), "true");
    ///
    /// let false_val = Bool::new(false);
    /// assert_eq!(format!("{}", false_val), "false");
    /// ```
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.value)
    }
}

// Logical operations using standard operators
impl std::ops::BitAnd for Bool {
    type Output = Self;

    #[inline]
    fn bitand(self, rhs: Self) -> Self::Output {
        self.and(rhs)
    }
}

impl std::ops::BitOr for Bool {
    type Output = Self;

    #[inline]
    fn bitor(self, rhs: Self) -> Self::Output {
        self.or(rhs)
    }
}

impl std::ops::BitXor for Bool {
    type Output = Self;

    #[inline]
    fn bitxor(self, rhs: Self) -> Self::Output {
        self.xor(rhs)
    }
}

impl std::ops::Not for Bool {
    type Output = Self;

    #[inline]
    fn not(self) -> Self::Output {
        self.not()
    }
}

// Assignment operations
impl std::ops::BitAndAssign for Bool {
    #[inline]
    fn bitand_assign(&mut self, rhs: Self) {
        self.value &= rhs.value;
    }
}

impl std::ops::BitOrAssign for Bool {
    #[inline]
    fn bitor_assign(&mut self, rhs: Self) {
        self.value |= rhs.value;
    }
}

impl std::ops::BitXorAssign for Bool {
    #[inline]
    fn bitxor_assign(&mut self, rhs: Self) {
        self.value ^= rhs.value;
    }
}

// Conversions from primitive types
impl From<bool> for Bool {
    #[inline]
    fn from(value: bool) -> Self {
        Self::new(value)
    }
}

// Conversions to primitive types
impl From<Bool> for bool {
    #[inline]
    fn from(bool_val: Bool) -> Self {
        bool_val.value
    }
}

impl From<Bool> for i64 {
    #[inline]
    fn from(bool_val: Bool) -> Self {
        if bool_val.value { 1 } else { 0 }
    }
}

impl From<Bool> for f64 {
    #[inline]
    fn from(bool_val: Bool) -> Self {
        if bool_val.value { 1.0 } else { 0.0 }
    }
}

// Variant system integration - From<Bool> is already implemented in variant.rs
impl TryFrom<super::Variant> for Bool {
    type Error = &'static str;

    /// ### Attempts to extract a Bool from a Variant.
    ///
    /// Tries to convert a Variant to a Bool. Succeeds if the Variant
    /// contains a BoolWrapper, otherwise returns an error.
    ///
    /// # Parameters
    /// - `variant`: The Variant to convert
    ///
    /// # Returns
    /// Result containing the Bool on success, error message on failure.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Bool, Variant};
    /// let variant = Variant::BoolWrapper(Bool::new(true));
    /// let bool_val: Bool = variant.try_into().unwrap();
    /// assert_eq!(bool_val.get(), true);
    /// ```
    fn try_from(variant: super::Variant) -> Result<Self, Self::Error> {
        match variant {
            super::Variant::BoolWrapper(bool_val) => Ok(bool_val),
            _ => Err("Variant is not a BoolWrapper"),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_bool_creation() {
        let true_val = Bool::new(true);
        assert_eq!(true_val.get(), true);

        let false_val = Bool::new(false);
        assert_eq!(false_val.get(), false);

        let from_primitive = Bool::from_primitive(true);
        assert_eq!(from_primitive.get(), true);

        let default_val = Bool::default();
        assert_eq!(default_val.get(), false);

        let from_bool: Bool = true.into();
        assert_eq!(from_bool.get(), true);
    }

    #[test]
    fn test_bool_constants() {
        assert_eq!(Bool::TRUE.get(), true);
        assert_eq!(Bool::FALSE.get(), false);
    }

    #[test]
    fn test_bool_value_access() {
        let mut bool_val = Bool::new(false);
        assert_eq!(bool_val.get(), false);
        assert_eq!(bool_val.as_primitive(), false);

        bool_val.set(true);
        assert_eq!(bool_val.get(), true);
    }

    #[test]
    fn test_bool_logical_operations() {
        let true_val = Bool::new(true);
        let false_val = Bool::new(false);

        // Test AND
        assert_eq!(true_val.and(true_val), Bool::new(true));
        assert_eq!(true_val.and(false_val), Bool::new(false));
        assert_eq!(false_val.and(true_val), Bool::new(false));
        assert_eq!(false_val.and(false_val), Bool::new(false));

        // Test OR
        assert_eq!(true_val.or(true_val), Bool::new(true));
        assert_eq!(true_val.or(false_val), Bool::new(true));
        assert_eq!(false_val.or(true_val), Bool::new(true));
        assert_eq!(false_val.or(false_val), Bool::new(false));

        // Test NOT
        assert_eq!(true_val.not(), Bool::new(false));
        assert_eq!(false_val.not(), Bool::new(true));

        // Test XOR
        assert_eq!(true_val.xor(true_val), Bool::new(false));
        assert_eq!(true_val.xor(false_val), Bool::new(true));
        assert_eq!(false_val.xor(true_val), Bool::new(true));
        assert_eq!(false_val.xor(false_val), Bool::new(false));
    }

    #[test]
    fn test_bool_operator_overloads() {
        let true_val = Bool::new(true);
        let false_val = Bool::new(false);

        // Test bitwise AND (&)
        assert_eq!(true_val & true_val, Bool::new(true));
        assert_eq!(true_val & false_val, Bool::new(false));

        // Test bitwise OR (|)
        assert_eq!(true_val | false_val, Bool::new(true));
        assert_eq!(false_val | false_val, Bool::new(false));

        // Test bitwise XOR (^)
        assert_eq!(true_val ^ false_val, Bool::new(true));
        assert_eq!(true_val ^ true_val, Bool::new(false));

        // Test NOT (!)
        assert_eq!(!true_val, Bool::new(false));
        assert_eq!(!false_val, Bool::new(true));
    }

    #[test]
    fn test_bool_assignment_operations() {
        let mut a = Bool::new(true);
        let false_val = Bool::new(false);

        a &= false_val;
        assert_eq!(a, Bool::new(false));

        a |= Bool::new(true);
        assert_eq!(a, Bool::new(true));

        a ^= Bool::new(true);
        assert_eq!(a, Bool::new(false));
    }

    #[test]
    fn test_bool_conversions() {
        let true_val = Bool::new(true);
        let false_val = Bool::new(false);

        // Test to other wrapper types
        assert_eq!(true_val.to_int().get(), 1);
        assert_eq!(false_val.to_int().get(), 0);

        assert_eq!(true_val.to_float().get(), 1.0);
        assert_eq!(false_val.to_float().get(), 0.0);

        assert_eq!(true_val.to_short().get(), 1);
        assert_eq!(false_val.to_short().get(), 0);

        assert_eq!(true_val.to_int8().get(), 1);
        assert_eq!(false_val.to_int8().get(), 0);

        // Test to primitive types
        let to_bool: bool = true_val.into();
        assert_eq!(to_bool, true);

        let to_i64: i64 = true_val.into();
        assert_eq!(to_i64, 1);

        let to_f64: f64 = false_val.into();
        assert_eq!(to_f64, 0.0);
    }

    #[test]
    fn test_bool_comparison() {
        let true_val1 = Bool::new(true);
        let true_val2 = Bool::new(true);
        let false_val = Bool::new(false);

        assert_eq!(true_val1, true_val2);
        assert_ne!(true_val1, false_val);
        assert!(false_val < true_val1);
        assert!(true_val1 > false_val);
        assert!(true_val1 >= true_val2);
        assert!(false_val <= true_val1);
    }

    #[test]
    fn test_bool_display() {
        assert_eq!(format!("{}", Bool::new(true)), "true");
        assert_eq!(format!("{}", Bool::new(false)), "false");
        assert_eq!(format!("{}", Bool::TRUE), "true");
        assert_eq!(format!("{}", Bool::FALSE), "false");
    }

    #[test]
    fn test_bool_hash() {
        use std::collections::HashMap;

        let mut map = HashMap::new();
        let true_key = Bool::new(true);
        let false_key = Bool::new(false);

        map.insert(true_key, "true value");
        map.insert(false_key, "false value");

        assert_eq!(map.get(&Bool::new(true)), Some(&"true value"));
        assert_eq!(map.get(&Bool::new(false)), Some(&"false value"));
        assert_eq!(map.len(), 2);
    }

    #[test]
    fn test_bool_copy_semantics() {
        let original = Bool::new(true);
        let copied = original;

        // Both should have the same value
        assert_eq!(original.get(), true);
        assert_eq!(copied.get(), true);

        // Modifying one shouldn't affect the other
        let mut mutable_copy = original;
        mutable_copy.set(false);
        assert_eq!(original.get(), true);
        assert_eq!(mutable_copy.get(), false);
    }

    #[test]
    fn test_bool_edge_cases() {
        // Test that Bool maintains proper boolean semantics
        let true_val = Bool::new(true);
        let false_val = Bool::new(false);

        // Double negation should return original
        assert_eq!(true_val.not().not(), true_val);
        assert_eq!(false_val.not().not(), false_val);

        // Identity laws
        assert_eq!(true_val.and(Bool::TRUE), true_val);
        assert_eq!(false_val.or(Bool::FALSE), false_val);

        // Absorption laws
        assert_eq!(true_val.or(true_val.and(false_val)), true_val);
        assert_eq!(false_val.and(false_val.or(true_val)), false_val);
    }
}

//! Comprehensive RandomNumberGenerator implementation for Godot-compatible seeded random number generation.
//!
//! This module provides a complete RandomNumberGenerator implementation that maintains compatibility with
//! <PERSON><PERSON>'s RandomNumberGenerator class while providing high-quality pseudo-random number generation
//! with deterministic seeding for reproducible gameplay. It uses a PCG (Permuted Congruential Generator)
//! algorithm for excellent statistical properties and performance.

use std::fmt;
use std::sync::Mutex;

/// ### A Godot-compatible RandomNumberGenerator for seeded pseudo-random number generation.
///
/// RandomNumberGenerator provides comprehensive random number generation with deterministic seeding
/// for reproducible gameplay, save/load compatibility, and high-quality statistical properties.
/// It maintains full compatibility with <PERSON><PERSON>'s RandomNumberGenerator class while providing
/// excellent performance for game development needs.
///
/// ## Algorithm
///
/// Uses a PCG (Permuted Congruential Generator) algorithm which provides:
/// - **Excellent Statistical Quality**: Passes rigorous statistical tests
/// - **High Performance**: Fast generation suitable for game loops
/// - **Good Period**: Long period before repetition
/// - **Deterministic**: Same seed always produces same sequence
///
/// ## Use Cases
///
/// RandomNumberGenerator is ideal for:
/// - **Procedural Generation**: Deterministic world/level generation
/// - **Gameplay Mechanics**: Consistent random events and outcomes
/// - **Save/Load Systems**: Reproducible random state persistence
/// - **Testing and Debugging**: Predictable random sequences for testing
/// - **Multiplayer Games**: Synchronized random events across clients
/// - **AI Behavior**: Consistent but varied NPC behavior patterns
///
/// # Examples
/// ```
/// # use verturion::core::variant::RandomNumberGenerator;
/// // Create with specific seed for reproducibility
/// let mut rng = RandomNumberGenerator::new();
/// rng.set_seed(12345);
///
/// // Generate various random values
/// let random_int = rng.randi();
/// let random_float = rng.randf();
/// let random_range = rng.randi_range(1, 100);
/// let random_bool = rng.randb();
///
/// // Same seed produces same sequence
/// let mut rng2 = RandomNumberGenerator::new();
/// rng2.set_seed(12345);
/// assert_eq!(rng2.randi(), random_int);
/// ```
#[derive(Debug)]
pub struct RandomNumberGenerator {
    /// Internal state for PCG algorithm
    state: Mutex<PcgState>,
}

/// Internal PCG state structure
#[derive(Debug, Clone)]
struct PcgState {
    /// Current state value
    state: u64,
    /// Stream increment (must be odd)
    inc: u64,
}

impl RandomNumberGenerator {
    /// ### Creates a new RandomNumberGenerator with a default seed.
    ///
    /// The generator is initialized with a default seed based on system time
    /// to provide different sequences on each run unless explicitly seeded.
    ///
    /// # Returns
    /// A new RandomNumberGenerator instance.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::RandomNumberGenerator;
    /// let mut rng = RandomNumberGenerator::new();
    /// let value = rng.randf();
    /// assert!(value >= 0.0 && value < 1.0);
    /// ```
    #[inline]
    pub fn new() -> Self {
        let mut rng = Self {
            state: Mutex::new(PcgState {
                state: 0x853c49e6748fea9b,
                inc: 0xda3e39cb94b95bdb,
            }),
        };

        // Initialize with a time-based seed for non-deterministic default behavior
        let seed = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_nanos() as u64;
        rng.set_seed(seed);
        rng
    }

    /// ### Creates a new RandomNumberGenerator with a specific seed.
    ///
    /// This constructor allows immediate seeding for deterministic behavior,
    /// which is essential for reproducible gameplay and testing.
    ///
    /// # Parameters
    /// - `seed`: The initial seed value for the generator
    ///
    /// # Returns
    /// A new RandomNumberGenerator instance with the specified seed.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::RandomNumberGenerator;
    /// let mut rng = RandomNumberGenerator::from_seed(42);
    /// let value1 = rng.randi();
    ///
    /// let mut rng2 = RandomNumberGenerator::from_seed(42);
    /// let value2 = rng2.randi();
    /// assert_eq!(value1, value2); // Same seed, same sequence
    /// ```
    #[inline]
    pub fn from_seed(seed: u64) -> Self {
        let mut rng = Self::new();
        rng.set_seed(seed);
        rng
    }

    /// ### Sets the seed for the random number generator.
    ///
    /// Setting the seed resets the generator state to a deterministic starting point.
    /// This is crucial for reproducible gameplay, save/load systems, and testing.
    ///
    /// # Parameters
    /// - `seed`: The seed value to initialize the generator
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::RandomNumberGenerator;
    /// let mut rng = RandomNumberGenerator::new();
    /// rng.set_seed(12345);
    ///
    /// let value1 = rng.randi();
    /// rng.set_seed(12345); // Reset to same seed
    /// let value2 = rng.randi();
    /// assert_eq!(value1, value2);
    /// ```
    #[inline]
    pub fn set_seed(&mut self, seed: u64) {
        let mut state = self.state.lock().unwrap();
        state.state = 0;
        state.inc = (seed.wrapping_shl(1)) | 1; // Ensure increment is odd

        // Advance the state once to mix the seed
        self.pcg_step(&mut state);
        state.state = state.state.wrapping_add(seed);
        self.pcg_step(&mut state);
    }

    /// ### Gets the current seed of the random number generator.
    ///
    /// Returns the seed that was used to initialize the current state.
    /// Note that this returns the original seed, not the current internal state.
    ///
    /// # Returns
    /// The current seed value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::RandomNumberGenerator;
    /// let mut rng = RandomNumberGenerator::new();
    /// rng.set_seed(54321);
    /// assert_eq!(rng.get_seed(), 54321);
    /// ```
    #[inline]
    pub fn get_seed(&self) -> u64 {
        let state = self.state.lock().unwrap();
        (state.inc.wrapping_sub(1)).wrapping_shr(1) // Reverse the seed encoding
    }

    /// ### Generates a random 32-bit unsigned integer.
    ///
    /// This is the core random generation method that produces uniformly
    /// distributed 32-bit integers across the full range [0, 2^32).
    ///
    /// # Returns
    /// A random 32-bit unsigned integer.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::RandomNumberGenerator;
    /// let mut rng = RandomNumberGenerator::from_seed(42);
    /// let value = rng.randi();
    /// assert!(value <= u32::MAX);
    /// ```
    #[inline]
    pub fn randi(&mut self) -> u32 {
        let mut state = self.state.lock().unwrap();
        self.pcg_step(&mut state);

        // PCG output function
        let xorshifted = (((state.state >> 18) ^ state.state) >> 27) as u32;
        let rot = (state.state >> 59) as u32;
        xorshifted.rotate_right(rot)
    }

    /// ### Generates a random floating-point number between 0.0 and 1.0.
    ///
    /// Produces uniformly distributed floating-point values in the range [0.0, 1.0).
    /// The distribution excludes 1.0 to maintain consistency with Godot's behavior.
    ///
    /// # Returns
    /// A random floating-point number in the range [0.0, 1.0).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::RandomNumberGenerator;
    /// let mut rng = RandomNumberGenerator::from_seed(42);
    /// let value = rng.randf();
    /// assert!(value >= 0.0 && value < 1.0);
    /// ```
    #[inline]
    pub fn randf(&mut self) -> f64 {
        // Generate 53 bits of precision for f64
        let a = (self.randi() >> 5) as u64;
        let b = (self.randi() >> 6) as u64;
        (a as f64 * 67108864.0 + b as f64) * (1.0 / 9007199254740992.0)
    }

    /// ### Generates a random integer within a specified range.
    ///
    /// Produces uniformly distributed integers in the inclusive range [from, to].
    /// Both bounds are included in the possible results.
    ///
    /// # Parameters
    /// - `from`: The minimum value (inclusive)
    /// - `to`: The maximum value (inclusive)
    ///
    /// # Returns
    /// A random integer in the range [from, to].
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::RandomNumberGenerator;
    /// let mut rng = RandomNumberGenerator::from_seed(42);
    /// let dice_roll = rng.randi_range(1, 6);
    /// assert!(dice_roll >= 1 && dice_roll <= 6);
    /// ```
    #[inline]
    pub fn randi_range(&mut self, from: i32, to: i32) -> i32 {
        if from > to {
            return from;
        }

        let range = (to as i64 - from as i64 + 1) as u32;
        if range == 0 {
            return from;
        }

        // Use rejection sampling to avoid bias
        let limit = u32::MAX - (u32::MAX % range);
        loop {
            let value = self.randi();
            if value < limit {
                return from + (value % range) as i32;
            }
        }
    }

    /// ### Generates a random floating-point number within a specified range.
    ///
    /// Produces uniformly distributed floating-point values in the range [from, to).
    /// The upper bound is exclusive to maintain consistency with Godot's behavior.
    ///
    /// # Parameters
    /// - `from`: The minimum value (inclusive)
    /// - `to`: The maximum value (exclusive)
    ///
    /// # Returns
    /// A random floating-point number in the range [from, to).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::RandomNumberGenerator;
    /// let mut rng = RandomNumberGenerator::from_seed(42);
    /// let temperature = rng.randf_range(-10.0, 40.0);
    /// assert!(temperature >= -10.0 && temperature < 40.0);
    /// ```
    #[inline]
    pub fn randf_range(&mut self, from: f64, to: f64) -> f64 {
        if from >= to {
            return from;
        }
        from + self.randf() * (to - from)
    }

    /// ### Generates a random boolean value.
    ///
    /// Produces true or false with equal probability (50% each).
    /// Useful for binary decisions and coin-flip scenarios.
    ///
    /// # Returns
    /// A random boolean value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::RandomNumberGenerator;
    /// let mut rng = RandomNumberGenerator::from_seed(42);
    /// let coin_flip = rng.randb();
    /// // coin_flip is either true or false
    /// ```
    #[inline]
    pub fn randb(&mut self) -> bool {
        self.randi() & 1 == 1
    }

    /// ### Generates a random boolean with specified probability.
    ///
    /// Produces true with the given probability and false otherwise.
    /// Probability should be in the range [0.0, 1.0].
    ///
    /// # Parameters
    /// - `probability`: The probability of returning true (0.0 to 1.0)
    ///
    /// # Returns
    /// A random boolean based on the specified probability.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::RandomNumberGenerator;
    /// let mut rng = RandomNumberGenerator::from_seed(42);
    /// let likely_true = rng.randb_probability(0.8); // 80% chance of true
    /// let unlikely_true = rng.randb_probability(0.2); // 20% chance of true
    /// ```
    #[inline]
    pub fn randb_probability(&mut self, probability: f64) -> bool {
        if probability <= 0.0 {
            return false;
        }
        if probability >= 1.0 {
            return true;
        }
        self.randf() < probability
    }

    /// ### Shuffles a mutable slice in place.
    ///
    /// Uses the Fisher-Yates shuffle algorithm to randomly permute the elements
    /// of the provided slice. This is useful for randomizing arrays, card decks,
    /// and other collections.
    ///
    /// # Parameters
    /// - `slice`: The mutable slice to shuffle
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::RandomNumberGenerator;
    /// let mut rng = RandomNumberGenerator::from_seed(42);
    /// let mut cards = vec![1, 2, 3, 4, 5];
    /// rng.shuffle(&mut cards);
    /// // cards is now randomly shuffled
    /// ```
    #[inline]
    pub fn shuffle<T>(&mut self, slice: &mut [T]) {
        for i in (1..slice.len()).rev() {
            let j = self.randi_range(0, i as i32) as usize;
            slice.swap(i, j);
        }
    }

    /// ### Selects a random element from a slice.
    ///
    /// Returns a reference to a randomly selected element from the provided slice.
    /// Returns None if the slice is empty.
    ///
    /// # Parameters
    /// - `slice`: The slice to select from
    ///
    /// # Returns
    /// An optional reference to a randomly selected element.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::RandomNumberGenerator;
    /// let mut rng = RandomNumberGenerator::from_seed(42);
    /// let colors = vec!["red", "green", "blue", "yellow"];
    /// if let Some(color) = rng.choose(&colors) {
    ///     println!("Selected color: {}", color);
    /// }
    /// ```
    #[inline]
    pub fn choose<'a, T>(&mut self, slice: &'a [T]) -> Option<&'a T> {
        if slice.is_empty() {
            return None;
        }
        let index = self.randi_range(0, slice.len() as i32 - 1) as usize;
        slice.get(index)
    }

    /// ### Gets the current internal state for serialization.
    ///
    /// Returns the internal state as a tuple that can be used to restore
    /// the generator to the exact same state later. This is useful for
    /// save/load systems and state synchronization.
    ///
    /// # Returns
    /// A tuple containing the internal state (state, increment).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::RandomNumberGenerator;
    /// let mut rng = RandomNumberGenerator::from_seed(42);
    /// let value1 = rng.randi();
    /// let state = rng.get_state();
    ///
    /// let value2 = rng.randi();
    /// rng.set_state(state);
    /// let value3 = rng.randi();
    /// assert_eq!(value2, value3); // Same state, same next value
    /// ```
    #[inline]
    pub fn get_state(&self) -> (u64, u64) {
        let state = self.state.lock().unwrap();
        (state.state, state.inc)
    }

    /// ### Sets the internal state from serialized data.
    ///
    /// Restores the generator to a specific internal state using data
    /// obtained from get_state(). This enables precise state restoration
    /// for save/load systems.
    ///
    /// # Parameters
    /// - `state_data`: A tuple containing the state (state, increment)
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::RandomNumberGenerator;
    /// let mut rng = RandomNumberGenerator::from_seed(42);
    /// let saved_state = rng.get_state();
    ///
    /// // Generate some numbers...
    /// rng.randi();
    /// rng.randi();
    ///
    /// // Restore to saved state
    /// rng.set_state(saved_state);
    /// ```
    #[inline]
    pub fn set_state(&mut self, state_data: (u64, u64)) {
        let mut state = self.state.lock().unwrap();
        state.state = state_data.0;
        state.inc = state_data.1;
    }

    /// ### Generates a random f32 value between 0.0 and 1.0.
    ///
    /// Similar to randf() but returns a 32-bit float for compatibility
    /// with systems that prefer single precision.
    ///
    /// # Returns
    /// A random f32 in the range [0.0, 1.0).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::RandomNumberGenerator;
    /// let mut rng = RandomNumberGenerator::from_seed(42);
    /// let value = rng.randf32();
    /// assert!(value >= 0.0 && value < 1.0);
    /// ```
    #[inline]
    pub fn randf32(&mut self) -> f32 {
        // Generate 24 bits of precision for f32
        let value = self.randi() >> 8;
        (value as f32) * (1.0 / 16777216.0)
    }

    /// ### Internal PCG step function.
    ///
    /// Advances the internal state using the PCG algorithm.
    /// This is the core of the pseudo-random number generation.
    ///
    /// # Parameters
    /// - `state`: Mutable reference to the PCG state
    #[inline]
    fn pcg_step(&self, state: &mut PcgState) {
        state.state = state.state.wrapping_mul(6364136223846793005).wrapping_add(state.inc);
    }
}

impl Default for RandomNumberGenerator {
    /// Returns a new RandomNumberGenerator with default initialization.
    #[inline]
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for RandomNumberGenerator {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "RandomNumberGenerator(seed: {})", self.get_seed())
    }
}

impl PartialEq for RandomNumberGenerator {
    /// Compares two RandomNumberGenerators based on their internal state.
    #[inline]
    fn eq(&self, other: &Self) -> bool {
        self.get_state() == other.get_state()
    }
}

impl Eq for RandomNumberGenerator {}

impl Clone for RandomNumberGenerator {
    /// Clones the RandomNumberGenerator, creating a new instance with the same state.
    #[inline]
    fn clone(&self) -> Self {
        let state = self.state.lock().unwrap();
        Self {
            state: Mutex::new(state.clone()),
        }
    }
}

// Note: Hash is intentionally not implemented as RNG state should not be used as hash keys

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_random_number_generator_creation() {
        let rng = RandomNumberGenerator::new();
        assert!(rng.get_seed() > 0); // Should have a time-based seed

        let rng_default = RandomNumberGenerator::default();
        assert!(rng_default.get_seed() > 0);

        let rng_seeded = RandomNumberGenerator::from_seed(42);
        assert_eq!(rng_seeded.get_seed(), 42);
    }

    #[test]
    fn test_random_number_generator_seeding() {
        let mut rng = RandomNumberGenerator::new();

        rng.set_seed(12345);
        assert_eq!(rng.get_seed(), 12345);

        let value1 = rng.randi();
        rng.set_seed(12345); // Reset to same seed
        let value2 = rng.randi();
        assert_eq!(value1, value2);

        rng.set_seed(54321);
        assert_eq!(rng.get_seed(), 54321);
        let value3 = rng.randi();
        assert_ne!(value1, value3); // Different seed should produce different values
    }

    #[test]
    fn test_random_number_generator_deterministic() {
        let mut rng1 = RandomNumberGenerator::from_seed(42);
        let mut rng2 = RandomNumberGenerator::from_seed(42);

        // Same seed should produce identical sequences
        for _ in 0..100 {
            assert_eq!(rng1.randi(), rng2.randi());
            assert_eq!(rng1.randf(), rng2.randf());
            assert_eq!(rng1.randb(), rng2.randb());
        }
    }

    #[test]
    fn test_random_number_generator_randi() {
        let mut rng = RandomNumberGenerator::from_seed(42);

        // Test that randi produces values in valid range
        for _ in 0..1000 {
            let value = rng.randi();
            assert!(value <= u32::MAX);
        }

        // Test that randi produces different values
        let mut values = std::collections::HashSet::new();
        for _ in 0..100 {
            values.insert(rng.randi());
        }
        assert!(values.len() > 90); // Should have high diversity
    }

    #[test]
    fn test_random_number_generator_randf() {
        let mut rng = RandomNumberGenerator::from_seed(42);

        // Test that randf produces values in [0.0, 1.0)
        for _ in 0..1000 {
            let value = rng.randf();
            assert!(value >= 0.0 && value < 1.0);
        }

        // Test distribution (should be roughly uniform)
        let mut count_low = 0;
        let mut count_high = 0;
        for _ in 0..10000 {
            let value = rng.randf();
            if value < 0.5 {
                count_low += 1;
            } else {
                count_high += 1;
            }
        }

        // Should be roughly 50/50 split (within 5% tolerance)
        let ratio = count_low as f64 / (count_low + count_high) as f64;
        assert!(ratio > 0.45 && ratio < 0.55);
    }

    #[test]
    fn test_random_number_generator_randi_range() {
        let mut rng = RandomNumberGenerator::from_seed(42);

        // Test basic range
        for _ in 0..1000 {
            let value = rng.randi_range(1, 6);
            assert!(value >= 1 && value <= 6);
        }

        // Test single value range
        for _ in 0..10 {
            let value = rng.randi_range(5, 5);
            assert_eq!(value, 5);
        }

        // Test inverted range (should return from value)
        for _ in 0..10 {
            let value = rng.randi_range(10, 5);
            assert_eq!(value, 10);
        }

        // Test negative ranges
        for _ in 0..100 {
            let value = rng.randi_range(-10, -5);
            assert!(value >= -10 && value <= -5);
        }

        // Test distribution within range
        let mut counts = [0; 6];
        for _ in 0..6000 {
            let value = rng.randi_range(1, 6);
            counts[(value - 1) as usize] += 1;
        }

        // Each value should appear roughly 1000 times (within 20% tolerance)
        for count in counts.iter() {
            assert!(*count > 800 && *count < 1200);
        }
    }

    #[test]
    fn test_random_number_generator_randf_range() {
        let mut rng = RandomNumberGenerator::from_seed(42);

        // Test basic range
        for _ in 0..1000 {
            let value = rng.randf_range(1.0, 5.0);
            assert!(value >= 1.0 && value < 5.0);
        }

        // Test negative ranges
        for _ in 0..100 {
            let value = rng.randf_range(-2.5, -1.0);
            assert!(value >= -2.5 && value < -1.0);
        }

        // Test inverted range (should return from value)
        for _ in 0..10 {
            let value = rng.randf_range(5.0, 1.0);
            assert_eq!(value, 5.0);
        }

        // Test equal range
        for _ in 0..10 {
            let value = rng.randf_range(3.14, 3.14);
            assert_eq!(value, 3.14);
        }
    }

    #[test]
    fn test_random_number_generator_randb() {
        let mut rng = RandomNumberGenerator::from_seed(42);

        let mut true_count = 0;
        let mut false_count = 0;

        for _ in 0..10000 {
            if rng.randb() {
                true_count += 1;
            } else {
                false_count += 1;
            }
        }

        // Should be roughly 50/50 split (within 5% tolerance)
        let ratio = true_count as f64 / (true_count + false_count) as f64;
        assert!(ratio > 0.45 && ratio < 0.55);
    }

    #[test]
    fn test_random_number_generator_randb_probability() {
        let mut rng = RandomNumberGenerator::from_seed(42);

        // Test 0% probability
        for _ in 0..100 {
            assert!(!rng.randb_probability(0.0));
        }

        // Test 100% probability
        for _ in 0..100 {
            assert!(rng.randb_probability(1.0));
        }

        // Test negative probability
        for _ in 0..100 {
            assert!(!rng.randb_probability(-0.5));
        }

        // Test > 1.0 probability
        for _ in 0..100 {
            assert!(rng.randb_probability(1.5));
        }

        // Test 80% probability
        let mut true_count = 0;
        for _ in 0..10000 {
            if rng.randb_probability(0.8) {
                true_count += 1;
            }
        }

        let ratio = true_count as f64 / 10000.0;
        assert!(ratio > 0.75 && ratio < 0.85); // Should be around 80%
    }

    #[test]
    fn test_random_number_generator_shuffle() {
        let mut rng = RandomNumberGenerator::from_seed(42);

        let mut original = vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
        let original_copy = original.clone();

        rng.shuffle(&mut original);

        // Should contain same elements
        let mut sorted = original.clone();
        sorted.sort();
        assert_eq!(sorted, original_copy);

        // Should be different order (very high probability)
        assert_ne!(original, original_copy);

        // Test empty slice
        let mut empty: Vec<i32> = vec![];
        rng.shuffle(&mut empty);
        assert!(empty.is_empty());

        // Test single element
        let mut single = vec![42];
        rng.shuffle(&mut single);
        assert_eq!(single, vec![42]);
    }

    #[test]
    fn test_random_number_generator_choose() {
        let mut rng = RandomNumberGenerator::from_seed(42);

        let options = vec!["apple", "banana", "cherry", "date"];

        // Test normal selection
        for _ in 0..100 {
            let choice = rng.choose(&options);
            assert!(choice.is_some());
            assert!(options.contains(choice.unwrap()));
        }

        // Test empty slice
        let empty: Vec<&str> = vec![];
        assert!(rng.choose(&empty).is_none());

        // Test single element
        let single = vec!["only"];
        for _ in 0..10 {
            assert_eq!(rng.choose(&single), Some(&"only"));
        }

        // Test distribution
        let mut counts = std::collections::HashMap::new();
        for _ in 0..4000 {
            if let Some(choice) = rng.choose(&options) {
                *counts.entry(*choice).or_insert(0) += 1;
            }
        }

        // Each option should be chosen roughly 1000 times (within 20% tolerance)
        for option in &options {
            let count = counts.get(option).unwrap_or(&0);
            assert!(*count > 800 && *count < 1200);
        }
    }

    #[test]
    fn test_random_number_generator_state_management() {
        let mut rng = RandomNumberGenerator::from_seed(42);

        let value1 = rng.randi();
        let state = rng.get_state();

        let value2 = rng.randi();
        let value3 = rng.randi();

        // Restore state and verify next value matches
        rng.set_state(state);
        let value4 = rng.randi();
        assert_eq!(value2, value4);

        let value5 = rng.randi();
        assert_eq!(value3, value5);
    }

    #[test]
    fn test_random_number_generator_randf32() {
        let mut rng = RandomNumberGenerator::from_seed(42);

        // Test that randf32 produces values in [0.0, 1.0)
        for _ in 0..1000 {
            let value = rng.randf32();
            assert!(value >= 0.0 && value < 1.0);
        }

        // Test precision (f32 should have less precision than f64)
        let f32_value = rng.randf32();
        let f64_value = rng.randf();

        // Both should be in valid range
        assert!(f32_value >= 0.0 && f32_value < 1.0);
        assert!(f64_value >= 0.0 && f64_value < 1.0);
    }

    #[test]
    fn test_random_number_generator_display() {
        let rng = RandomNumberGenerator::from_seed(12345);
        let display_str = format!("{}", rng);
        assert!(display_str.contains("RandomNumberGenerator"));
        assert!(display_str.contains("12345"));
    }

    #[test]
    fn test_random_number_generator_equality() {
        let rng1 = RandomNumberGenerator::from_seed(42);
        let rng2 = RandomNumberGenerator::from_seed(42);
        let rng3 = RandomNumberGenerator::from_seed(43);

        assert_eq!(rng1, rng2);
        assert_ne!(rng1, rng3);

        // Test after state changes
        let mut rng4 = RandomNumberGenerator::from_seed(42);
        let mut rng5 = RandomNumberGenerator::from_seed(42);

        rng4.randi(); // Advance state
        assert_ne!(rng4, rng5);

        rng5.randi(); // Advance to same state
        assert_eq!(rng4, rng5);
    }

    #[test]
    fn test_random_number_generator_clone() {
        let mut rng1 = RandomNumberGenerator::from_seed(42);
        rng1.randi(); // Advance state

        let mut rng2 = rng1.clone();
        assert_eq!(rng1, rng2);

        // Both should produce same sequence from this point
        assert_eq!(rng1.randi(), rng2.randi());
    }

    #[test]
    fn test_random_number_generator_edge_cases() {
        let mut rng = RandomNumberGenerator::from_seed(0);

        // Test with seed 0
        assert_eq!(rng.get_seed(), 0);
        let value = rng.randi();
        assert!(value <= u32::MAX);

        // Test with maximum seed (note: due to encoding, max representable seed is different)
        let max_seed = (u64::MAX - 1) >> 1; // Maximum seed that can be properly encoded/decoded
        rng.set_seed(max_seed);
        assert_eq!(rng.get_seed(), max_seed);
        let value = rng.randi();
        assert!(value <= u32::MAX);

        // Test range edge cases
        assert_eq!(rng.randi_range(i32::MIN, i32::MIN), i32::MIN);
        assert_eq!(rng.randi_range(i32::MAX, i32::MAX), i32::MAX);

        let value = rng.randi_range(i32::MIN, i32::MAX);
        assert!(value >= i32::MIN && value <= i32::MAX);
    }

    #[test]
    fn test_random_number_generator_statistical_quality() {
        let mut rng = RandomNumberGenerator::from_seed(42);

        // Test that consecutive values are not correlated
        let mut values = Vec::new();
        for _ in 0..1000 {
            values.push(rng.randi());
        }

        // Simple test: no obvious patterns in low bits
        let mut low_bit_changes = 0;
        for i in 1..values.len() {
            if (values[i] & 1) != (values[i-1] & 1) {
                low_bit_changes += 1;
            }
        }

        // Should change roughly 50% of the time (within 10% tolerance)
        let change_ratio = low_bit_changes as f64 / (values.len() - 1) as f64;
        assert!(change_ratio > 0.4 && change_ratio < 0.6);
    }

    #[test]
    fn test_random_number_generator_thread_safety() {
        use std::sync::Arc;
        use std::thread;

        let rng = Arc::new(RandomNumberGenerator::from_seed(42));
        let mut handles = vec![];

        // Spawn multiple threads that read from the RNG
        for _ in 0..4 {
            let rng_clone = Arc::clone(&rng);
            let handle = thread::spawn(move || {
                // Just test that we can access the RNG from multiple threads
                let _seed = rng_clone.get_seed();
                let _state = rng_clone.get_state();
            });
            handles.push(handle);
        }

        // Wait for all threads to complete
        for handle in handles {
            handle.join().unwrap();
        }
    }

    #[test]
    fn test_random_number_generator_performance_characteristics() {
        let mut rng = RandomNumberGenerator::from_seed(42);

        // Test that the generator can produce many values quickly
        let start = std::time::Instant::now();
        for _ in 0..100000 {
            rng.randi();
        }
        let duration = start.elapsed();

        // Should be able to generate 100k values in reasonable time (< 100ms)
        assert!(duration.as_millis() < 100);

        // Test randf performance
        let start = std::time::Instant::now();
        for _ in 0..100000 {
            rng.randf();
        }
        let duration = start.elapsed();

        // randf should also be fast (< 200ms due to additional computation)
        assert!(duration.as_millis() < 200);
    }
}
